class Admin::Settings::ThemesController < Admin::ApplicationController
  layout "admin/settings"

  def show
    add_breadcrumb "Nastavení webu", admin_settings_path
    add_breadcrumb "Téma"
  end

  def update
    if current_tenant.update(theme_params)
      redirect_to admin_settings_theme_path, notice: "Nastavení tématu by<PERSON>."
    else
      render :show, status: :unprocessable_entity
    end
  end

  def load_preset
    template_key = params[:template_key]

    if current_tenant.load_template!(template_key) && current_tenant.save
      redirect_to admin_settings_theme_path, notice: "Šablona '#{current_tenant.available_templates[template_key.to_sym][:name]}' by<PERSON>."
    else
      redirect_to admin_settings_theme_path, alert: "Nepodařilo se načíst šablonu."
    end
  end

  private

  def theme_params
    # Základní parametry
    permitted_params = [:template_key, :font_family, :heading_font_family, :border_radius, :button_style]

    # <PERSON><PERSON><PERSON> př<PERSON>j parametry pro témata, k<PERSON><PERSON> mají definované barvy
    current_tenant.available_color_themes.each do |theme_name, theme_data|
      if theme_data.is_a?(Hash) && theme_data.key?(:base)
        permitted_params += [
          "#{theme_name}_base".to_sym,
          "#{theme_name}_base_content".to_sym,
          "#{theme_name}_accent".to_sym,
          "#{theme_name}_accent_content".to_sym
        ]
      end
    end

    params.require(:website).permit(permitted_params)
  end
end
